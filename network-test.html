<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Network Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f0f0f0;
            padding: 20px;
            text-align: center;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: 0 auto;
        }
        .success {
            color: #28a745;
            font-size: 24px;
            font-weight: bold;
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .qr-container {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .link {
            font-size: 18px;
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
            display: block;
            margin: 15px 0;
            padding: 15px;
            background: #e7f3ff;
            border-radius: 8px;
            border: 2px solid #007bff;
        }
        .link:hover {
            background: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 Network Connection Successful!</h1>
        <div class="success">✅ Your mobile can access the server!</div>
        
        <div class="info">
            <strong>📱 You're connected from:</strong><br>
            <span id="userAgent"></span>
        </div>
        
        <div class="qr-container">
            <h3>📷 QR Code for Easy Sharing</h3>
            <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=http://**************:8000" 
                 alt="QR Code" style="width: 200px; height: 200px;">
        </div>
        
        <h3>🔗 Quick Access Links:</h3>
        <a href="http://**************:8000" class="link">🏠 Main App</a>
        <a href="http://**************:8000/camera-test.html" class="link">📷 Camera Test</a>
        <a href="http://**************:8000/register.html" class="link">👤 Register</a>
        <a href="http://**************:8000/login.html" class="link">🔐 Login</a>
        
        <div class="info">
            <strong>💡 Next Steps:</strong><br>
            1. Bookmark this page for easy access<br>
            2. Test the camera functionality<br>
            3. Register your face for authentication<br>
            4. Start using facial recognition login!
        </div>
    </div>

    <script>
        document.getElementById('userAgent').textContent = navigator.userAgent;
        
        // Test if we can reach the main server
        fetch('http://**************:8000')
            .then(response => {
                if (response.ok) {
                    console.log('✅ Server is accessible');
                }
            })
            .catch(error => {
                console.log('❌ Server connection issue:', error);
            });
    </script>
</body>
</html>

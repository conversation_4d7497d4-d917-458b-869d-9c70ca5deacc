@echo off
title Facial Authentication HTTPS Server

echo.
echo ========================================
echo  Facial Authentication HTTPS Server
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Node.js found, starting HTTPS server...
    echo.
    echo HTTPS URLs:
    echo Desktop: https://localhost:8443
    echo Mobile:  https://**************:8443
    echo.
    echo Note: You may see security warnings - click "Advanced" then "Proceed"
    echo.
    node -e "
    const https = require('https');
    const fs = require('fs');
    const path = require('path');
    const url = require('url');

    // Create self-signed certificate
    const selfsigned = require('selfsigned');
    const attrs = [{ name: 'commonName', value: 'localhost' }];
    const pems = selfsigned.generate(attrs, { days: 365 });

    const server = https.createServer({
        key: pems.private,
        cert: pems.cert
    }, (req, res) => {
        const parsedUrl = url.parse(req.url);
        let pathname = parsedUrl.pathname;

        if (pathname === '/') pathname = '/index.html';

        const filePath = path.join(__dirname, pathname);

        fs.readFile(filePath, (err, data) => {
            if (err) {
                res.writeHead(404);
                res.end('File not found');
                return;
            }

            const ext = path.extname(filePath);
            const contentType = {
                '.html': 'text/html',
                '.css': 'text/css',
                '.js': 'application/javascript',
                '.json': 'application/json'
            }[ext] || 'text/plain';

            res.writeHead(200, { 'Content-Type': contentType });
            res.end(data);
        });
    });

    server.listen(8443, '0.0.0.0', () => {
        console.log('HTTPS Server running on https://0.0.0.0:8443');
    });
    "
) else (
    echo Node.js not found, falling back to HTTP server...
    echo.
    echo HTTP URLs:
    echo Desktop: http://localhost:8000
    echo Mobile:  http://**************:8000
    echo.
    python server.py
)

pause
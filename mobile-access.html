<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Access - Facial Authentication</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        
        .title {
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
            font-weight: bold;
        }
        
        .qr-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border: 2px dashed #667eea;
        }
        
        .qr-code {
            width: 200px;
            height: 200px;
            margin: 0 auto 20px;
            background: white;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #ddd;
        }
        
        .link-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
        
        .mobile-link {
            font-size: 18px;
            color: #667eea;
            text-decoration: none;
            font-weight: bold;
            word-break: break-all;
            display: block;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 2px solid #667eea;
            transition: all 0.3s ease;
        }
        
        .mobile-link:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 15px;
            transition: all 0.3s ease;
        }
        
        .copy-btn:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        
        .instructions {
            text-align: left;
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
        }
        
        .instructions h3 {
            color: #1976d2;
            margin-top: 0;
        }
        
        .instructions ol {
            color: #333;
            line-height: 1.6;
        }
        
        .status {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .emoji {
            font-size: 24px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">📱 Mobile Access Portal</h1>
        
        <div class="status">
            <span class="emoji">✅</span>
            <strong>Server Status:</strong> Running on Network
        </div>
        
        <div class="qr-container">
            <div class="qr-code" id="qrcode">
                <div style="color: #666; font-size: 14px;">
                    📷 QR Code<br>
                    <small>Scan with mobile camera</small>
                </div>
            </div>
            <p style="color: #666; margin: 0;">Scan this QR code with your mobile camera</p>
        </div>
        
        <div class="link-container">
            <h3 style="margin-top: 0; color: #333;">🔗 Direct Link</h3>
            <a href="http://**************:8000" class="mobile-link" id="mobileLink" target="_blank">
                http://**************:8000
            </a>
            <button class="copy-btn" onclick="copyLink()">📋 Copy Link</button>
        </div>
        
        <div class="instructions">
            <h3>📋 Instructions</h3>
            <ol>
                <li><strong>Connect to Wi-Fi:</strong> Ensure your mobile is on the same Wi-Fi network</li>
                <li><strong>Scan QR Code:</strong> Use your mobile camera to scan the QR code above</li>
                <li><strong>Or Click Link:</strong> Tap the blue link above on your mobile device</li>
                <li><strong>Allow Camera:</strong> Grant camera permissions when prompted</li>
                <li><strong>Start Using:</strong> Register or login with facial recognition</li>
            </ol>
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background: #fff3cd; border-radius: 8px; border: 1px solid #ffeaa7;">
            <strong>💡 Tip:</strong> Bookmark this link on your mobile for easy access!
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script>
        // Generate QR Code
        const canvas = document.createElement('canvas');
        const qrContainer = document.getElementById('qrcode');
        
        QRCode.toCanvas(canvas, 'http://**************:8000', {
            width: 180,
            margin: 2,
            color: {
                dark: '#667eea',
                light: '#ffffff'
            }
        }, function (error) {
            if (error) {
                console.error(error);
                qrContainer.innerHTML = '<div style="color: red;">QR Code generation failed</div>';
            } else {
                qrContainer.innerHTML = '';
                qrContainer.appendChild(canvas);
            }
        });
        
        // Copy link function
        function copyLink() {
            const link = document.getElementById('mobileLink').href;
            navigator.clipboard.writeText(link).then(function() {
                const btn = document.querySelector('.copy-btn');
                const originalText = btn.innerHTML;
                btn.innerHTML = '✅ Copied!';
                btn.style.background = '#28a745';
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.style.background = '#28a745';
                }, 2000);
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = link;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('Link copied to clipboard!');
            });
        }
        
        // Auto-refresh server status (optional)
        setInterval(() => {
            fetch('http://**************:8000')
                .then(response => {
                    if (response.ok) {
                        document.querySelector('.status').innerHTML = 
                            '<span class="emoji">✅</span><strong>Server Status:</strong> Running on Network';
                        document.querySelector('.status').style.background = '#d4edda';
                        document.querySelector('.status').style.color = '#155724';
                    }
                })
                .catch(() => {
                    document.querySelector('.status').innerHTML = 
                        '<span class="emoji">❌</span><strong>Server Status:</strong> Offline';
                    document.querySelector('.status').style.background = '#f8d7da';
                    document.querySelector('.status').style.color = '#721c24';
                });
        }, 10000); // Check every 10 seconds
    </script>
</body>
</html>

#!/usr/bin/env python3
"""
HTTPS server for the Facial Authentication System
Serves the application over HTTPS for better camera support
"""

import http.server
import socketserver
import ssl
import os
import sys
import webbrowser
from pathlib import Path
import tempfile

def create_self_signed_cert():
    """Create a self-signed certificate for HTTPS"""
    try:
        import subprocess
        
        # Create temporary directory for certificates
        cert_dir = tempfile.mkdtemp()
        cert_file = os.path.join(cert_dir, 'server.crt')
        key_file = os.path.join(cert_dir, 'server.key')
        
        # Generate self-signed certificate using OpenSSL
        cmd = [
            'openssl', 'req', '-x509', '-newkey', 'rsa:4096', '-keyout', key_file,
            '-out', cert_file, '-days', '365', '-nodes', '-subj',
            '/C=US/ST=State/L=City/O=Organization/CN=localhost'
        ]
        
        subprocess.run(cmd, check=True, capture_output=True)
        return cert_file, key_file
        
    except (subprocess.CalledProcessError, FileNotFoundError):
        return None, None

def get_local_ip():
    """Get the local IP address"""
    import socket
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except Exception:
        return "localhost"

def find_free_port(start_port=8443, max_attempts=10):
    """Find a free port starting from start_port"""
    import socket
    
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    
    raise RuntimeError(f"Could not find a free port in range {start_port}-{start_port + max_attempts}")

def print_startup_info(port, use_https=False):
    """Print startup information and instructions"""
    local_ip = get_local_ip()
    protocol = "https" if use_https else "http"
    
    print("\n" + "="*70)
    print("🔐 Facial Authentication System Server (HTTPS)")
    print("="*70)
    print(f"📡 Server running on:")
    print(f"   • Local:   {protocol}://localhost:{port}")
    print(f"   • Network: {protocol}://{local_ip}:{port}")
    print(f"📁 Serving directory: {os.getcwd()}")
    
    if use_https:
        print("\n🔒 HTTPS Mode:")
        print("   • Better camera support on mobile")
        print("   • You may see security warnings (click 'Advanced' → 'Proceed')")
        print("   • Self-signed certificate (safe for development)")
    
    print("\n📋 Instructions:")
    print("🖥️  For Desktop:")
    print(f"   1. Navigate to: {protocol}://localhost:{port}")
    print("\n📱 For Mobile/Other Devices:")
    print(f"   1. Connect to the same Wi-Fi network")
    print(f"   2. Navigate to: {protocol}://{local_ip}:{port}")
    print("   3. Accept security warning if prompted")
    print("   4. Allow camera permissions when prompted")
    print("   5. Start with user registration")
    print("\n⚠️  Important Notes:")
    print("• HTTPS provides better camera support on mobile")
    print("• Accept security warnings for self-signed certificate")
    print("• Keep this terminal window open while using the app")
    print("• Mobile devices must be on the same Wi-Fi network")
    print("\n🛑 To stop the server: Press Ctrl+C")
    print("="*70)

def main():
    """Main server function"""
    print("🚀 Starting Facial Authentication System HTTPS Server...")
    
    # Check if required files exist
    required_files = ['index.html', 'register.html', 'login.html', 'dashboard.html']
    missing_files = [f for f in required_files if not Path(f).exists()]
    
    if missing_files:
        print("❌ Missing required files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        print("\nPlease ensure all application files are present.")
        sys.exit(1)
    
    print("✅ All required files found.")
    
    try:
        # Find a free port
        port = find_free_port()
        
        # Try to create HTTPS server
        cert_file, key_file = create_self_signed_cert()
        
        if cert_file and key_file:
            # HTTPS server
            with socketserver.TCPServer(("0.0.0.0", port), http.server.SimpleHTTPRequestHandler) as httpd:
                context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
                context.load_cert_chain(cert_file, key_file)
                httpd.socket = context.wrap_socket(httpd.socket, server_side=True)
                
                print_startup_info(port, use_https=True)
                
                # Try to open browser automatically
                try:
                    webbrowser.open(f'https://localhost:{port}')
                    print("🌐 Browser opened automatically")
                except Exception:
                    print("🌐 Please open your browser manually")
                
                print(f"\n⏳ HTTPS Server starting on port {port}...")
                httpd.serve_forever()
        else:
            # Fallback to HTTP
            print("⚠️  Could not create HTTPS certificate, falling back to HTTP")
            with socketserver.TCPServer(("0.0.0.0", 8000), http.server.SimpleHTTPRequestHandler) as httpd:
                print_startup_info(8000, use_https=False)
                
                try:
                    webbrowser.open(f'http://localhost:8000')
                    print("🌐 Browser opened automatically")
                except Exception:
                    print("🌐 Please open your browser manually")
                
                print(f"\n⏳ HTTP Server starting on port 8000...")
                httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
        print("Thank you for using the Facial Authentication System!")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        print("Falling back to regular HTTP server...")
        
        # Final fallback
        try:
            with socketserver.TCPServer(("0.0.0.0", 8000), http.server.SimpleHTTPRequestHandler) as httpd:
                print_startup_info(8000, use_https=False)
                httpd.serve_forever()
        except Exception as e2:
            print(f"❌ Final fallback failed: {e2}")
            sys.exit(1)

if __name__ == "__main__":
    main()

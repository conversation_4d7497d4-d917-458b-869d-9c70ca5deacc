#!/usr/bin/env python3
"""
Simple HTTP server for the Facial Authentication System
Serves the application over HTTP
"""

import http.server
import socketserver
import os
import sys
import webbrowser
from pathlib import Path

def find_free_port(start_port=8000, max_attempts=10):
    """Find a free port starting from start_port"""
    import socket
    
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    
    raise RuntimeError(f"Could not find a free port in range {start_port}-{start_port + max_attempts}")

def check_requirements():
    """Check if all required files exist"""
    required_files = [
        'index.html',
        'register.html',
        'login.html',
        'dashboard.html',
        'styles/main.css',
        'js/app.js',
        'js/faceAuth.js',
        'js/camera.js',
        'js/storage.js',
        'js/utils.js'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing required files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        print("\nPlease ensure all application files are present.")
        return False
    
    print("✅ All required files found.")
    return True

def get_local_ip():
    """Get the local IP address"""
    import socket
    try:
        # Connect to a remote address to determine local IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except Exception:
        return "localhost"

def print_startup_info(port):
    """Print startup information and instructions"""
    local_ip = get_local_ip()
    print("\n" + "="*70)
    print("🔐 Facial Authentication System Server")
    print("="*70)
    print(f"📡 Server running on:")
    print(f"   • Local:   http://localhost:{port}")
    print(f"   • Network: http://{local_ip}:{port}")
    print(f"📁 Serving directory: {os.getcwd()}")
    print("\n📋 Instructions:")
    print("🖥️  For Desktop:")
    print(f"   1. Navigate to: http://localhost:{port}")
    print("\n📱 For Mobile/Other Devices:")
    print(f"   1. Connect to the same Wi-Fi network")
    print(f"   2. Navigate to: http://{local_ip}:{port}")
    print("   3. Allow camera permissions when prompted")
    print("   4. Start with user registration")
    print("\n⚠️  Important Notes:")
    print("• Camera access requires HTTPS in production")
    print("• This server is for development only")
    print("• Keep this terminal window open while using the app")
    print("• Mobile devices must be on the same Wi-Fi network")
    print("\n🛑 To stop the server: Press Ctrl+C")
    print("="*70)

def main():
    """Main server function"""
    print("🚀 Starting Facial Authentication System Server...")
    
    # Check if we're in the right directory
    if not check_requirements():
        sys.exit(1)
    
    try:
        # Find a free port
        port = find_free_port()
        
        # Create server - bind to all interfaces for mobile access
        with socketserver.TCPServer(("0.0.0.0", port), http.server.SimpleHTTPRequestHandler) as httpd:
            print_startup_info(port)
            
            # Try to open browser automatically
            try:
                webbrowser.open(f'http://localhost:{port}')
                print("🌐 Browser opened automatically")
            except Exception:
                print("🌐 Please open your browser manually")
            
            print(f"\n⏳ Server starting on port {port}...")
            
            # Start serving
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
        print("Thank you for using the Facial Authentication System!")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        print("Please check the error message and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()

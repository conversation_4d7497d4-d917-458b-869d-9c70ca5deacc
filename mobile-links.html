<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 Mobile Access Links</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .title {
            color: #333;
            margin-bottom: 30px;
            font-size: 32px;
            font-weight: bold;
        }
        
        .link-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            text-decoration: none;
            display: block;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .link-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            text-decoration: none;
            color: white;
        }
        
        .link-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .link-url {
            font-size: 18px;
            font-family: 'Courier New', monospace;
            background: rgba(255,255,255,0.2);
            padding: 10px;
            border-radius: 8px;
            word-break: break-all;
        }
        
        .qr-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            border: 2px dashed #667eea;
        }
        
        .qr-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .qr-item {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .qr-item img {
            width: 150px;
            height: 150px;
            border-radius: 8px;
        }
        
        .qr-item h4 {
            margin: 15px 0 5px 0;
            color: #333;
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 25px;
            border-radius: 15px;
            margin: 30px 0;
            text-align: left;
        }
        
        .instructions h3 {
            color: #1976d2;
            margin-top: 0;
            text-align: center;
        }
        
        .step {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #667eea;
        }
        
        .step-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        
        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        
        .copy-btn:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        
        .status {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }
        
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">📱 Mobile Access Portal</h1>
        
        <div class="status">
            <strong>✅ Server Status:</strong> Running on your local network
        </div>
        
        <!-- Direct Links -->
        <h2 style="color: #333; margin-top: 40px;">🔗 Click These Links on Your Mobile</h2>
        
        <a href="http://**************:8000" class="link-card" target="_blank">
            <div class="link-title">🏠 Main Application</div>
            <div class="link-url">http://**************:8000</div>
        </a>
        
        <a href="http://**************:8000/camera-test.html" class="link-card" target="_blank">
            <div class="link-title">📷 Camera Test</div>
            <div class="link-url">http://**************:8000/camera-test.html</div>
        </a>
        
        <a href="http://**************:8000/register.html" class="link-card" target="_blank">
            <div class="link-title">👤 Register New User</div>
            <div class="link-url">http://**************:8000/register.html</div>
        </a>
        
        <a href="http://**************:8000/login.html" class="link-card" target="_blank">
            <div class="link-title">🔐 Login with Face</div>
            <div class="link-url">http://**************:8000/login.html</div>
        </a>
        
        <!-- QR Codes -->
        <div class="qr-section">
            <h3 style="color: #333; margin-top: 0;">📷 QR Codes - Scan with Mobile Camera</h3>
            <div class="qr-grid">
                <div class="qr-item">
                    <img src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=http://**************:8000" alt="Main App QR">
                    <h4>Main App</h4>
                    <button class="copy-btn" onclick="copyText('http://**************:8000')">📋 Copy</button>
                </div>
                <div class="qr-item">
                    <img src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=http://**************:8000/camera-test.html" alt="Camera Test QR">
                    <h4>Camera Test</h4>
                    <button class="copy-btn" onclick="copyText('http://**************:8000/camera-test.html')">📋 Copy</button>
                </div>
                <div class="qr-item">
                    <img src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=http://**************:8000/register.html" alt="Register QR">
                    <h4>Register</h4>
                    <button class="copy-btn" onclick="copyText('http://**************:8000/register.html')">📋 Copy</button>
                </div>
            </div>
        </div>
        
        <!-- Instructions -->
        <div class="instructions">
            <h3>📋 How to Access on Mobile</h3>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>Connect to Wi-Fi:</strong> Make sure your mobile is on the same Wi-Fi network as this computer
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>Choose Method:</strong>
                <br>• <strong>QR Code:</strong> Scan any QR code above with your mobile camera
                <br>• <strong>Direct Link:</strong> Click any blue link above on your mobile
                <br>• <strong>Manual:</strong> Type <code>**************:8000</code> in mobile browser
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>Allow Permissions:</strong> When prompted, allow camera access for facial recognition
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                <strong>Start Using:</strong> Register your face or login with facial authentication
            </div>
        </div>
        
        <div class="warning">
            <strong>💡 Troubleshooting:</strong>
            <br>• If links don't work, try the Camera Test first
            <br>• Make sure both devices are on the same Wi-Fi
            <br>• Try different mobile browsers (Chrome, Safari, Firefox)
            <br>• Clear mobile browser cache if needed
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
            <strong>📱 Share These Links:</strong>
            <br>Send via WhatsApp, SMS, or email to access from any mobile device on your network!
        </div>
    </div>

    <script>
        function copyText(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Find the button that was clicked
                const buttons = document.querySelectorAll('.copy-btn');
                buttons.forEach(btn => {
                    if (btn.onclick.toString().includes(text)) {
                        const originalText = btn.innerHTML;
                        btn.innerHTML = '✅ Copied!';
                        btn.style.background = '#28a745';
                        setTimeout(() => {
                            btn.innerHTML = originalText;
                            btn.style.background = '#28a745';
                        }, 2000);
                    }
                });
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
                alert('Link copied: ' + text);
            });
        }
        
        // Test server connectivity
        function testConnectivity() {
            fetch('http://**************:8000')
                .then(response => {
                    if (response.ok) {
                        document.querySelector('.status').innerHTML = 
                            '<strong>✅ Server Status:</strong> Online and accessible';
                        document.querySelector('.status').style.background = '#d4edda';
                    }
                })
                .catch(() => {
                    document.querySelector('.status').innerHTML = 
                        '<strong>❌ Server Status:</strong> Cannot connect to server';
                    document.querySelector('.status').style.background = '#f8d7da';
                    document.querySelector('.status').style.color = '#721c24';
                });
        }
        
        // Test connectivity on load
        window.addEventListener('load', testConnectivity);
        
        // Test every 30 seconds
        setInterval(testConnectivity, 30000);
    </script>
</body>
</html>

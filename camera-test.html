<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Test - Facial Authentication</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .title {
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
            font-weight: bold;
        }
        
        .video-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 2px dashed #667eea;
        }
        
        #video {
            width: 100%;
            max-width: 400px;
            height: 300px;
            background: #000;
            border-radius: 10px;
            object-fit: cover;
        }
        
        .button {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .button:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .device-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: left;
        }
        
        .device-info h3 {
            margin-top: 0;
            color: #333;
        }
        
        .troubleshooting {
            background: #fff3cd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: left;
            border: 1px solid #ffeaa7;
        }
        
        .troubleshooting h3 {
            margin-top: 0;
            color: #856404;
        }
        
        .troubleshooting ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .troubleshooting li {
            margin: 8px 0;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">📷 Camera Test</h1>
        
        <div id="status" class="status info">
            Click "Test Camera" to check if your camera is working
        </div>
        
        <div class="video-container">
            <video id="video" autoplay muted playsinline>
                Your browser does not support video
            </video>
        </div>
        
        <button id="testBtn" class="button" onclick="testCamera()">📷 Test Camera</button>
        <button id="stopBtn" class="button" onclick="stopCamera()" disabled>⏹️ Stop Camera</button>
        
        <div class="device-info">
            <h3>📱 Device Information</h3>
            <div id="deviceInfo">Loading device information...</div>
        </div>
        
        <div class="troubleshooting">
            <h3>🔧 Troubleshooting Tips</h3>
            <ul>
                <li><strong>Permission Denied:</strong> Allow camera access in browser settings</li>
                <li><strong>No Camera Found:</strong> Check if camera is being used by another app</li>
                <li><strong>HTTPS Required:</strong> Some browsers require HTTPS for camera access</li>
                <li><strong>Browser Issues:</strong> Try Chrome, Firefox, or Safari</li>
                <li><strong>Mobile Issues:</strong> Ensure you're using the device's default browser</li>
            </ul>
        </div>
        
        <div style="margin-top: 30px;">
            <a href="index.html" class="button">🏠 Back to Main App</a>
        </div>
    </div>

    <script>
        let stream = null;
        const video = document.getElementById('video');
        const status = document.getElementById('status');
        const testBtn = document.getElementById('testBtn');
        const stopBtn = document.getElementById('stopBtn');
        
        // Display device information
        function displayDeviceInfo() {
            const info = {
                'User Agent': navigator.userAgent,
                'Platform': navigator.platform,
                'Language': navigator.language,
                'Online': navigator.onLine,
                'Cookie Enabled': navigator.cookieEnabled,
                'Screen Resolution': `${screen.width}x${screen.height}`,
                'Viewport Size': `${window.innerWidth}x${window.innerHeight}`,
                'Protocol': window.location.protocol,
                'Host': window.location.host
            };
            
            let infoHtml = '';
            for (const [key, value] of Object.entries(info)) {
                infoHtml += `<strong>${key}:</strong> ${value}<br>`;
            }
            
            document.getElementById('deviceInfo').innerHTML = infoHtml;
        }
        
        // Test camera function
        async function testCamera() {
            updateStatus('info', '🔄 Requesting camera access...');
            testBtn.disabled = true;
            
            try {
                // Check if getUserMedia is supported
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    throw new Error('getUserMedia is not supported in this browser');
                }
                
                // Request camera access
                const constraints = {
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 },
                        facingMode: 'user'
                    },
                    audio: false
                };
                
                stream = await navigator.mediaDevices.getUserMedia(constraints);
                video.srcObject = stream;
                
                updateStatus('success', '✅ Camera is working! You should see video above.');
                stopBtn.disabled = false;
                
                // Get camera info
                const videoTrack = stream.getVideoTracks()[0];
                const settings = videoTrack.getSettings();
                console.log('Camera settings:', settings);
                
            } catch (error) {
                console.error('Camera error:', error);
                
                let errorMessage = '❌ Camera access failed: ';
                
                switch (error.name) {
                    case 'NotAllowedError':
                        errorMessage += 'Permission denied. Please allow camera access.';
                        break;
                    case 'NotFoundError':
                        errorMessage += 'No camera found on this device.';
                        break;
                    case 'NotSupportedError':
                        errorMessage += 'Camera not supported in this browser.';
                        break;
                    case 'NotReadableError':
                        errorMessage += 'Camera is being used by another application.';
                        break;
                    case 'SecurityError':
                        errorMessage += 'Security error. Try using HTTPS.';
                        break;
                    default:
                        errorMessage += error.message || 'Unknown error occurred.';
                }
                
                updateStatus('error', errorMessage);
                testBtn.disabled = false;
            }
        }
        
        // Stop camera function
        function stopCamera() {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
                video.srcObject = null;
                updateStatus('info', '⏹️ Camera stopped.');
                testBtn.disabled = false;
                stopBtn.disabled = true;
            }
        }
        
        // Update status message
        function updateStatus(type, message) {
            status.className = `status ${type}`;
            status.textContent = message;
        }
        
        // Check for available cameras
        async function checkCameras() {
            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                const cameras = devices.filter(device => device.kind === 'videoinput');
                
                if (cameras.length === 0) {
                    updateStatus('error', '❌ No cameras found on this device.');
                } else {
                    console.log('Available cameras:', cameras);
                }
            } catch (error) {
                console.error('Error checking cameras:', error);
            }
        }
        
        // Initialize
        window.addEventListener('load', () => {
            displayDeviceInfo();
            checkCameras();
        });
        
        // Clean up on page unload
        window.addEventListener('beforeunload', () => {
            stopCamera();
        });
    </script>
</body>
</html>

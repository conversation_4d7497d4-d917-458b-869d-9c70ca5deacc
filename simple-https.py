#!/usr/bin/env python3
"""
Simple HTTPS server for facial authentication app
"""

import http.server
import socketserver
import ssl
import os
import sys
import tempfile
import subprocess

def create_self_signed_cert():
    """Create a simple self-signed certificate"""
    try:
        # Create temporary files for cert and key
        cert_file = "server.crt"
        key_file = "server.key"
        
        # Create self-signed certificate using OpenSSL
        cmd = [
            'openssl', 'req', '-x509', '-newkey', 'rsa:2048', '-keyout', key_file,
            '-out', cert_file, '-days', '1', '-nodes', '-subj',
            '/C=US/ST=State/L=City/O=Dev/CN=localhost'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0 and os.path.exists(cert_file) and os.path.exists(key_file):
            return cert_file, key_file
        else:
            print("OpenSSL not available, trying alternative method...")
            return create_simple_cert()
            
    except Exception as e:
        print(f"Certificate creation failed: {e}")
        return create_simple_cert()

def create_simple_cert():
    """Create a very basic certificate using Python"""
    try:
        from cryptography import x509
        from cryptography.x509.oid import NameOID
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.primitives.asymmetric import rsa
        from cryptography.hazmat.primitives import serialization
        import datetime
        
        # Generate private key
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
        )
        
        # Create certificate
        subject = issuer = x509.Name([
            x509.NameAttribute(NameOID.COMMON_NAME, u"localhost"),
        ])
        
        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            issuer
        ).public_key(
            private_key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.datetime.utcnow()
        ).not_valid_after(
            datetime.datetime.utcnow() + datetime.timedelta(days=1)
        ).sign(private_key, hashes.SHA256())
        
        # Write certificate and key to files
        cert_file = "server.crt"
        key_file = "server.key"
        
        with open(cert_file, "wb") as f:
            f.write(cert.public_bytes(serialization.Encoding.PEM))
        
        with open(key_file, "wb") as f:
            f.write(private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))
        
        return cert_file, key_file
        
    except ImportError:
        print("Cryptography library not available")
        return None, None
    except Exception as e:
        print(f"Certificate creation failed: {e}")
        return None, None

def get_local_ip():
    """Get the local IP address"""
    import socket
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except Exception:
        return "localhost"

def main():
    port = 8443
    local_ip = get_local_ip()
    
    print("🔒 Starting HTTPS Server for Facial Authentication...")
    print("=" * 60)
    
    # Try to create certificate
    cert_file, key_file = create_self_signed_cert()
    
    if not cert_file or not key_file:
        print("❌ Could not create HTTPS certificate")
        print("💡 Falling back to HTTP server...")
        print("=" * 60)
        
        # Fallback to HTTP
        with socketserver.TCPServer(("0.0.0.0", 8000), http.server.SimpleHTTPRequestHandler) as httpd:
            print(f"📡 HTTP Server running on:")
            print(f"   • Desktop: http://localhost:8000")
            print(f"   • Mobile:  http://{local_ip}:8000")
            print("=" * 60)
            httpd.serve_forever()
        return
    
    try:
        # Create HTTPS server
        with socketserver.TCPServer(("0.0.0.0", port), http.server.SimpleHTTPRequestHandler) as httpd:
            # Wrap socket with SSL
            context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
            context.load_cert_chain(cert_file, key_file)
            httpd.socket = context.wrap_socket(httpd.socket, server_side=True)
            
            print("✅ HTTPS Certificate created successfully!")
            print(f"📡 HTTPS Server running on:")
            print(f"   • Desktop: https://localhost:{port}")
            print(f"   • Mobile:  https://{local_ip}:{port}")
            print()
            print("⚠️  Security Warning:")
            print("   • You will see 'Not Secure' warnings")
            print("   • Click 'Advanced' → 'Proceed to localhost'")
            print("   • This is normal for self-signed certificates")
            print()
            print("📱 Mobile Instructions:")
            print(f"   1. Open browser and go to: https://{local_ip}:{port}")
            print("   2. Accept security warning")
            print("   3. Allow camera permissions")
            print("   4. Start using facial authentication!")
            print()
            print("🛑 Press Ctrl+C to stop server")
            print("=" * 60)
            
            # Start serving
            httpd.serve_forever()
            
    except Exception as e:
        print(f"❌ HTTPS server failed: {e}")
        print("💡 Falling back to HTTP...")
        
        # Final fallback to HTTP
        with socketserver.TCPServer(("0.0.0.0", 8000), http.server.SimpleHTTPRequestHandler) as httpd:
            print(f"📡 HTTP Server running on:")
            print(f"   • Desktop: http://localhost:8000")
            print(f"   • Mobile:  http://{local_ip}:8000")
            httpd.serve_forever()
    
    finally:
        # Clean up certificate files
        for file in [cert_file, key_file]:
            if file and os.path.exists(file):
                try:
                    os.remove(file)
                except:
                    pass

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
        print("Thank you for using the Facial Authentication System!")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
